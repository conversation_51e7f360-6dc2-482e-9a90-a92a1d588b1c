using System;
using System.ComponentModel;
using System.Text.Json.Serialization;
using System.Windows;

namespace PC_Control2.Demo.Models
{
    /// <summary>
    /// SFC跳转元素模型 - 符合IEC 61131-3标准的跳转元素
    /// 实现从当前位置跳转到指定步骤的功能
    /// </summary>
    public class SFCJumpModel : SFCElementModelBase
    {
        #region 私有字段

        private string _targetStepId = string.Empty;
        private int _targetStepNumber = 0;
        private string _targetStepName = string.Empty;
        private string _jumpCondition = string.Empty;
        private bool _isUnconditionalJump = true;
        private bool _isTargetValid = false;
        private string _validationMessage = string.Empty;
        private string _associatedTransitionId = string.Empty;
        private int _associatedTransitionNumber = 0;

        #endregion

        #region 跳转目标属性

        /// <summary>
        /// 跳转目标步骤ID
        /// </summary>
        public string TargetStepId
        {
            get => _targetStepId;
            set => SetProperty(ref _targetStepId, value);
        }

        /// <summary>
        /// 跳转目标步骤编号（用于显示）
        /// </summary>
        public int TargetStepNumber
        {
            get => _targetStepNumber;
            set => SetProperty(ref _targetStepNumber, value);
        }

        /// <summary>
        /// 跳转目标步骤名称（用于显示和验证）
        /// </summary>
        public string TargetStepName
        {
            get => _targetStepName;
            set => SetProperty(ref _targetStepName, value);
        }

        #endregion

        #region IEC 61131-3标准属性

        /// <summary>
        /// 跳转条件表达式（符合IEC 61131-3标准）
        /// 为空时表示无条件跳转
        /// </summary>
        public string JumpCondition
        {
            get => _jumpCondition;
            set => SetProperty(ref _jumpCondition, value);
        }

        /// <summary>
        /// 是否为无条件跳转
        /// </summary>
        public bool IsUnconditionalJump
        {
            get => _isUnconditionalJump;
            set => SetProperty(ref _isUnconditionalJump, value);
        }

        #endregion

        #region 验证和状态属性

        /// <summary>
        /// 跳转目标是否有效
        /// </summary>
        public bool IsTargetValid
        {
            get => _isTargetValid;
            set => SetProperty(ref _isTargetValid, value);
        }

        /// <summary>
        /// 验证消息（错误或警告信息）
        /// </summary>
        public string ValidationMessage
        {
            get => _validationMessage;
            set => SetProperty(ref _validationMessage, value);
        }

        #endregion

        #region 关联转换条件属性

        /// <summary>
        /// 关联的转换条件ID
        /// </summary>
        public string AssociatedTransitionId
        {
            get => _associatedTransitionId;
            set => SetProperty(ref _associatedTransitionId, value);
        }

        /// <summary>
        /// 关联的转换条件序号（用于显示）
        /// </summary>
        public int AssociatedTransitionNumber
        {
            get => _associatedTransitionNumber;
            set => SetProperty(ref _associatedTransitionNumber, value);
        }

        #endregion

        #region 显示属性

        /// <summary>
        /// 显示文本 - 显示跳转目标步骤号
        /// </summary>
        public string JumpDisplayText => TargetStepNumber > 0 ? $"S{TargetStepNumber}" : "S?";

        /// <summary>
        /// 指示器显示文本 - 显示关联的转换条件序号
        /// </summary>
        public string IndicatorDisplayText => AssociatedTransitionNumber > 0 ? $"T{AssociatedTransitionNumber}" : "T?";

        /// <summary>
        /// 完整显示文本 - 包含目标步骤名称
        /// </summary>
        public string FullDisplayText => TargetStepNumber > 0
            ? $"S{TargetStepNumber} - {TargetStepName}"
            : "未设置跳转目标";

        #endregion

        #region 重写基类属性和方法

        /// <summary>
        /// 元素类型
        /// </summary>
        public override SFCElementType ElementType => SFCElementType.Jump;

        /// <summary>
        /// 显示文本（重写基类）
        /// </summary>
        public override string DisplayText => JumpDisplayText;

        /// <summary>
        /// 初始化连接点
        /// </summary>
        public override void InitializeConnectPoints()
        {
            // 清空现有连接点
            ClearConnectPoints();

            // 跳转元素只有一个输入连接点（顶部），没有输出连接点
            // 因为跳转会直接跳转到目标步骤，不会继续向下执行
            AddInputConnectPoint(SFCDataFlowType.ControlFlow);
        }

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public SFCJumpModel()
        {
            // 设置默认名称
            Name = "Jump";

            // 设置默认尺寸（参考西门子Graph的跳转元素尺寸）
            Size = new Size(60, 50);

            // 初始化连接点
            InitializeConnectPoints();
        }

        #endregion

        #region 跳转目标管理方法

        /// <summary>
        /// 设置跳转目标
        /// </summary>
        /// <param name="targetStepId">目标步骤ID</param>
        /// <param name="targetStepNumber">目标步骤编号</param>
        /// <param name="targetStepName">目标步骤名称</param>
        public void SetJumpTarget(string targetStepId, int targetStepNumber, string targetStepName)
        {
            TargetStepId = targetStepId;
            TargetStepNumber = targetStepNumber;
            TargetStepName = targetStepName;
            
            // 更新验证状态
            ValidateJumpTarget();
        }

        /// <summary>
        /// 清除跳转目标
        /// </summary>
        public void ClearJumpTarget()
        {
            TargetStepId = string.Empty;
            TargetStepNumber = 0;
            TargetStepName = string.Empty;
            IsTargetValid = false;
            ValidationMessage = "未设置跳转目标";
        }

        /// <summary>
        /// 设置关联的转换条件
        /// </summary>
        /// <param name="transitionId">转换条件ID</param>
        /// <param name="transitionNumber">转换条件序号</param>
        public void SetAssociatedTransition(string transitionId, int transitionNumber)
        {
            AssociatedTransitionId = transitionId;
            AssociatedTransitionNumber = transitionNumber;
        }

        /// <summary>
        /// 清除关联的转换条件
        /// </summary>
        public void ClearAssociatedTransition()
        {
            AssociatedTransitionId = string.Empty;
            AssociatedTransitionNumber = 0;
        }

        /// <summary>
        /// 验证跳转目标
        /// </summary>
        private void ValidateJumpTarget()
        {
            if (string.IsNullOrEmpty(TargetStepId))
            {
                IsTargetValid = false;
                ValidationMessage = "未设置跳转目标";
                return;
            }

            if (TargetStepNumber <= 0)
            {
                IsTargetValid = false;
                ValidationMessage = "无效的目标步骤编号";
                return;
            }

            // 基础验证通过
            IsTargetValid = true;
            ValidationMessage = string.Empty;
        }

        #endregion

        #region 序列化支持

        /// <summary>
        /// 运行时状态（不序列化）
        /// </summary>
        [JsonIgnore]
        public bool IsExecuting { get; set; } = false;

        /// <summary>
        /// 最后执行时间（不序列化）
        /// </summary>
        [JsonIgnore]
        public DateTime LastExecutionTime { get; set; } = DateTime.MinValue;

        #endregion
    }
}
