using System.Windows;
using System.Windows.Controls;

namespace PC_Control2.Demo.Controls
{
    /// <summary>
    /// SFCJumpArrowIndicator.xaml 的交互逻辑
    /// 跳转箭头指示器 - 显示在目标步骤上方
    /// </summary>
    public partial class SFCJumpArrowIndicator : UserControl
    {
        #region 依赖属性

        /// <summary>
        /// 转换条件序号依赖属性
        /// </summary>
        public static readonly DependencyProperty TransitionNumberProperty =
            DependencyProperty.Register(nameof(TransitionNumber), typeof(int), typeof(SFCJumpArrowIndicator),
                new PropertyMetadata(0));

        /// <summary>
        /// 转换条件序号
        /// </summary>
        public int TransitionNumber
        {
            get => (int)GetValue(TransitionNumberProperty);
            set => SetValue(TransitionNumberProperty, value);
        }

        /// <summary>
        /// 目标位置依赖属性
        /// </summary>
        public static readonly DependencyProperty TargetPositionProperty =
            DependencyProperty.Register(nameof(TargetPosition), typeof(Point), typeof(SFCJumpArrowIndicator),
                new PropertyMetadata(new Point(0, 0), OnTargetPositionChanged));

        /// <summary>
        /// 目标位置
        /// </summary>
        public Point TargetPosition
        {
            get => (Point)GetValue(TargetPositionProperty);
            set => SetValue(TargetPositionProperty, value);
        }

        #endregion

        #region 构造函数

        public SFCJumpArrowIndicator()
        {
            InitializeComponent();
            DataContext = this;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 目标位置变化处理
        /// </summary>
        private static void OnTargetPositionChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is SFCJumpArrowIndicator indicator)
            {
                indicator.UpdatePosition();
            }
        }

        /// <summary>
        /// 更新箭头位置
        /// </summary>
        private void UpdatePosition()
        {
            // 在目标步骤上方8像素处显示箭头，使其更贴近目标步骤
            Canvas.SetLeft(this, TargetPosition.X - Width / 2 + 15);
            Canvas.SetTop(this, TargetPosition.Y + 10);
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 设置跳转信息
        /// </summary>
        /// <param name="transitionNumber">转换条件序号</param>
        /// <param name="targetPosition">目标位置</param>
        public void SetJumpInfo(int transitionNumber, Point targetPosition)
        {
            TransitionNumber = transitionNumber;
            TargetPosition = targetPosition;
        }

        /// <summary>
        /// 更新目标位置
        /// </summary>
        /// <param name="targetPosition">新的目标位置</param>
        public void UpdateTargetPosition(Point targetPosition)
        {
            TargetPosition = targetPosition;
        }

        #endregion
    }
}
