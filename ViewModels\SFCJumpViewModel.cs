using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Input;
using PC_Control2.Demo.Models;

namespace PC_Control2.Demo.ViewModels
{
    /// <summary>
    /// SFC跳转元素的ViewModel
    /// </summary>
    public class SFCJumpViewModel : ViewModelBase
    {
        #region 私有字段

        private string _id = string.Empty;
        private string _name = string.Empty;
        private string _description = string.Empty;
        private Point _position = new Point(0, 0);
        private Size _size = new Size(60, 50);
        private bool _isSelected = false;
        private string _targetStepId = string.Empty;
        private int _targetStepNumber = 0;
        private string _targetStepName = string.Empty;
        private bool _isTargetValid = false;
        private string _validationMessage = string.Empty;
        private string _jumpCondition = string.Empty;
        private bool _isUnconditionalJump = true;
        private string _associatedTransitionId = string.Empty;
        private int _associatedTransitionNumber = 0;

        #endregion

        #region 基本属性

        public string Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        public Point Position
        {
            get => _position;
            set => SetProperty(ref _position, value);
        }

        public Size Size
        {
            get => _size;
            set => SetProperty(ref _size, value);
        }

        public bool IsSelected
        {
            get => _isSelected;
            set => SetProperty(ref _isSelected, value);
        }

        #endregion

        #region 跳转目标属性

        public string TargetStepId
        {
            get => _targetStepId;
            set => SetProperty(ref _targetStepId, value);
        }

        public int TargetStepNumber
        {
            get => _targetStepNumber;
            set => SetProperty(ref _targetStepNumber, value);
        }

        public string TargetStepName
        {
            get => _targetStepName;
            set => SetProperty(ref _targetStepName, value);
        }

        public bool IsTargetValid
        {
            get => _isTargetValid;
            set => SetProperty(ref _isTargetValid, value);
        }

        public string ValidationMessage
        {
            get => _validationMessage;
            set => SetProperty(ref _validationMessage, value);
        }

        public string JumpCondition
        {
            get => _jumpCondition;
            set => SetProperty(ref _jumpCondition, value);
        }

        public bool IsUnconditionalJump
        {
            get => _isUnconditionalJump;
            set => SetProperty(ref _isUnconditionalJump, value);
        }

        /// <summary>
        /// 关联的转换条件ID
        /// </summary>
        public string AssociatedTransitionId
        {
            get => _associatedTransitionId;
            set => SetProperty(ref _associatedTransitionId, value);
        }

        /// <summary>
        /// 关联的转换条件序号（用于显示）
        /// </summary>
        public int AssociatedTransitionNumber
        {
            get => _associatedTransitionNumber;
            set => SetProperty(ref _associatedTransitionNumber, value);
        }

        #endregion

        #region 显示属性

        /// <summary>
        /// 跳转显示文本
        /// </summary>
        public string JumpDisplayText => TargetStepNumber > 0 ? $"S{TargetStepNumber}" : "S?";

        /// <summary>
        /// 指示器显示文本 - 显示关联的转换条件序号
        /// </summary>
        public string IndicatorDisplayText => AssociatedTransitionNumber > 0 ? $"T{AssociatedTransitionNumber}" : "T?";

        /// <summary>
        /// 完整显示文本
        /// </summary>
        public string FullDisplayText => TargetStepNumber > 0
            ? $"S{TargetStepNumber} - {TargetStepName}"
            : "未设置跳转目标";

        /// <summary>
        /// 显示文本（用于通用显示）
        /// </summary>
        public string DisplayText => JumpDisplayText;

        #endregion

        #region 命令

        public ICommand? SelectCommand { get; set; }
        public ICommand? DeleteCommand { get; set; }
        public ICommand? StartConnectionCommand { get; set; }
        public ICommand? EditJumpTargetCommand { get; set; }
        public ICommand? EditPropertiesCommand { get; set; }

        #endregion

        #region 连接点管理

        /// <summary>
        /// 连接点适配器集合
        /// </summary>
        public ObservableCollection<SFCConnectPointAdapter> ConnectPointAdapters { get; } = new ObservableCollection<SFCConnectPointAdapter>();

        #endregion

        #region 构造函数

        public SFCJumpViewModel()
        {
            // 设置默认值
            Id = Guid.NewGuid().ToString();
            Name = "Jump";
            Description = "SFC跳转元素";
            
            // 初始化连接点适配器
            InitializeConnectPointAdapters();
        }

        /// <summary>
        /// 从模型创建ViewModel
        /// </summary>
        /// <param name="model">跳转模型</param>
        public SFCJumpViewModel(SFCJumpModel model)
        {
            // 复制模型属性
            Id = model.Id;
            Name = model.Name;
            Description = model.Description;
            Position = model.Position;
            Size = model.Size;
            IsSelected = model.IsSelected;
            TargetStepId = model.TargetStepId;
            TargetStepNumber = model.TargetStepNumber;
            TargetStepName = model.TargetStepName;
            IsTargetValid = model.IsTargetValid;
            ValidationMessage = model.ValidationMessage;
            JumpCondition = model.JumpCondition;
            IsUnconditionalJump = model.IsUnconditionalJump;
            AssociatedTransitionId = model.AssociatedTransitionId;
            AssociatedTransitionNumber = model.AssociatedTransitionNumber;

            // 初始化连接点适配器
            InitializeConnectPointAdapters();
        }

        #endregion

        #region 跳转目标管理

        /// <summary>
        /// 设置跳转目标
        /// </summary>
        /// <param name="targetStepId">目标步骤ID</param>
        /// <param name="targetStepNumber">目标步骤编号</param>
        /// <param name="targetStepName">目标步骤名称</param>
        public void SetJumpTarget(string targetStepId, int targetStepNumber, string targetStepName)
        {
            TargetStepId = targetStepId;
            TargetStepNumber = targetStepNumber;
            TargetStepName = targetStepName;
            
            // 更新验证状态
            ValidateJumpTarget();
            
            // 通知显示属性变化
            OnPropertyChanged(nameof(JumpDisplayText));
            OnPropertyChanged(nameof(FullDisplayText));
            OnPropertyChanged(nameof(DisplayText));
        }

        /// <summary>
        /// 清除跳转目标
        /// </summary>
        public void ClearJumpTarget()
        {
            TargetStepId = string.Empty;
            TargetStepNumber = 0;
            TargetStepName = string.Empty;
            IsTargetValid = false;
            ValidationMessage = "未设置跳转目标";
            
            // 通知显示属性变化
            OnPropertyChanged(nameof(JumpDisplayText));
            OnPropertyChanged(nameof(FullDisplayText));
            OnPropertyChanged(nameof(DisplayText));
        }

        /// <summary>
        /// 验证跳转目标
        /// </summary>
        private void ValidateJumpTarget()
        {
            if (string.IsNullOrEmpty(TargetStepId))
            {
                IsTargetValid = false;
                ValidationMessage = "未设置跳转目标";
                return;
            }

            if (TargetStepNumber <= 0)
            {
                IsTargetValid = false;
                ValidationMessage = "无效的目标步骤编号";
                return;
            }

            // 基础验证通过
            IsTargetValid = true;
            ValidationMessage = string.Empty;
        }

        #endregion

        #region 连接点初始化

        /// <summary>
        /// 初始化连接点适配器
        /// </summary>
        private void InitializeConnectPointAdapters()
        {
            ConnectPointAdapters.Clear();

            // 跳转元素只有一个输入连接点（顶部）
            var inputAdapter = new SFCConnectPointAdapter(
                Id,
                SFCElementType.Jump,
                ConnectPointDirection.Input,
                0,
                SFCDataFlowType.ControlFlow);

            ConnectPointAdapters.Add(inputAdapter);
        }

        #endregion

        #region 模型转换

        /// <summary>
        /// 转换为数据模型
        /// </summary>
        /// <returns>SFCJumpModel</returns>
        public SFCJumpModel ToModel()
        {
            var model = new SFCJumpModel
            {
                Id = Id,
                Name = Name,
                Description = Description,
                Position = Position,
                Size = Size,
                IsSelected = IsSelected,
                TargetStepId = TargetStepId,
                TargetStepNumber = TargetStepNumber,
                TargetStepName = TargetStepName,
                IsTargetValid = IsTargetValid,
                ValidationMessage = ValidationMessage,
                JumpCondition = JumpCondition,
                IsUnconditionalJump = IsUnconditionalJump,
                AssociatedTransitionId = AssociatedTransitionId,
                AssociatedTransitionNumber = AssociatedTransitionNumber
            };

            return model;
        }

        #endregion
    }
}
