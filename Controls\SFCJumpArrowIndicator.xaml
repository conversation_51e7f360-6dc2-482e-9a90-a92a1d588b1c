<UserControl x:Class="PC_Control2.Demo.Controls.SFCJumpArrowIndicator"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             Width="60" Height="20">

    <!-- 跳转箭头指示器 - 显示在目标步骤上方 -->
    <Border Background="#FF4A90E2" 
            BorderBrush="#FF2E5A87" 
            BorderThickness="1"
            CornerRadius="3"
            Opacity="0.9">
        
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧箭头 -->
            <Path Grid.Column="0" 
                  Data="M0,10 L8,5 L8,15 Z" 
                  Fill="White" 
                  Margin="4,0,2,0"
                  VerticalAlignment="Center"/>

            <!-- 中间文本 -->
            <TextBlock Grid.Column="1"
                       Text="{Binding TransitionNumber, StringFormat=T{0}}"
                       Foreground="White"
                       FontSize="10"
                       FontWeight="Bold"
                       HorizontalAlignment="Center"
                       VerticalAlignment="Center"/>

            <!-- 右侧箭头 -->
            <Path Grid.Column="2" 
                  Data="M0,5 L8,10 L0,15 Z" 
                  Fill="White" 
                  Margin="2,0,4,0"
                  VerticalAlignment="Center"/>
        </Grid>
    </Border>
</UserControl>
