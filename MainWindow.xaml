<Window x:Class="PC_Control2.Demo.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="clr-namespace:PC_Control2.Demo.ViewModels"
        xmlns:views="clr-namespace:PC_Control2.Demo.Views"
        Title="{Binding Title}"
        Height="900" Width="1400"
        WindowStyle="None"
        AllowsTransparency="False"
        Background="#FF2D2D30"
        UseLayoutRounding="True"
        SnapsToDevicePixels="True"
        ResizeMode="CanResize"
        Loaded="Window_Loaded">

    <WindowChrome.WindowChrome>
        <WindowChrome CaptionHeight="32"
                      ResizeBorderThickness="5"
                      UseAeroCaptionButtons="False"
                      NonClientFrameEdges="None"
                      GlassFrameThickness="0"/>
    </WindowChrome.WindowChrome>

    <Window.Resources>
        <!-- 转换器 -->
        <BooleanToVisibilityConverter x:Key="BoolToVisConverter"/>

        <!-- UE蓝图风格颜色定义 -->
        <SolidColorBrush x:Key="UE_DarkBackground" Color="#FF101010"/>
        <SolidColorBrush x:Key="UE_MediumBackground" Color="#FF1A1A1A"/>
        <SolidColorBrush x:Key="UE_LightBackground" Color="#FF1A1A1A"/>
        <SolidColorBrush x:Key="UE_VeryLightBackground" Color="#FF2F2F2F"/>
        <SolidColorBrush x:Key="UE_BluePrimary" Color="#FF242424"/>
        <SolidColorBrush x:Key="UE_BlueSecondary" Color="#FF106EBE"/>
        <SolidColorBrush x:Key="UE_BlueLight" Color="#FF40A9FF"/>
        <SolidColorBrush x:Key="UE_TextPrimary" Color="#FFB0B0B0"/>
        <SolidColorBrush x:Key="UE_TextSecondary" Color="#FFA6A6A6"/>
        <SolidColorBrush x:Key="UE_TextDisabled" Color="#FF808080"/>
        <SolidColorBrush x:Key="UE_Border" Color="#FF5A5A5A"/>
        <SolidColorBrush x:Key="UE_BorderLight" Color="#FF707070"/>
        <SolidColorBrush x:Key="UE_Accent" Color="#FF00BCF2"/>
        <SolidColorBrush x:Key="UE_Success" Color="#FF00C851"/>
        <SolidColorBrush x:Key="UE_Warning" Color="#FFFFBB33"/>
        <SolidColorBrush x:Key="UE_Error" Color="#FFFF4444"/>

        <!-- 菜单项样式 -->
        <Style x:Key="MenuItemStyle" TargetType="MenuItem">
            <Setter Property="Foreground" Value="{StaticResource UE_TextPrimary}"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
            <Setter Property="Padding" Value="2,4"/>
            <Setter Property="Margin" Value="0"/>
            <Setter Property="MinWidth" Value="40"/>  <!-- 设置最小宽度60像素 -->
            <Setter Property="UseLayoutRounding" Value="True"/>
            <Setter Property="SnapsToDevicePixels" Value="True"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="MenuItem">
                        <Border x:Name="Border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                Padding="2,2"
                                MinWidth="40">
                            <Grid MinWidth="40">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <!-- Icon -->
                                <ContentPresenter x:Name="Icon"
                                                Grid.Column="0"
                                                ContentSource="Icon"
                                                VerticalAlignment="Center"
                                                HorizontalAlignment="Center"
                                                Margin="0,0,8,0"/>

                                <!-- Header -->
                                <ContentPresenter x:Name="HeaderHost"
                                                Grid.Column="1"
                                                ContentSource="Header"
                                                VerticalAlignment="Center"
                                                HorizontalAlignment="Center"
                                                TextElement.Foreground="{TemplateBinding Foreground}"/>

                                <!-- InputGestureText -->
                                <TextBlock x:Name="InputGestureText"
                                         Grid.Column="2"
                                         Text="{TemplateBinding InputGestureText}"
                                         Margin="16,0,0,0"
                                         VerticalAlignment="Center"
                                         Foreground="{StaticResource UE_TextSecondary}"/>

                                <!-- Arrow for submenu -->
                                <Path x:Name="ArrowPanel"
                                    Grid.Column="3"
                                    Data="M 0,0 L 4,4 L 0,8 Z"
                                    Fill="{StaticResource UE_TextSecondary}"
                                    Margin="8,0,0,0"
                                    VerticalAlignment="Center"
                                    Visibility="Collapsed"/>

                                <!-- Popup for submenu -->
                                <Popup x:Name="PART_Popup"
                                     AllowsTransparency="True"
                                     Focusable="False"
                                     IsOpen="{Binding IsSubmenuOpen, RelativeSource={RelativeSource TemplatedParent}}"
                                     Placement="Right"
                                     PopupAnimation="Fade">
                                    <Border Background="{StaticResource UE_MediumBackground}"
                                            BorderBrush="{StaticResource UE_Border}"
                                            BorderThickness="1"
                                            CornerRadius="2"
                                            Padding="2">
                                        <ScrollViewer Style="{DynamicResource {ComponentResourceKey ResourceId=MenuScrollViewer, TypeInTargetAssembly={x:Type FrameworkElement}}}">
                                            <ItemsPresenter KeyboardNavigation.DirectionalNavigation="Cycle"/>
                                        </ScrollViewer>
                                    </Border>
                                </Popup>
                            </Grid>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="Role" Value="TopLevelHeader">
                                <Setter Property="Margin" Value="0"/>
                                <Setter TargetName="PART_Popup" Property="Placement" Value="Bottom"/>
                                <Setter Property="FontWeight" Value="Normal"/>
                                <Setter TargetName="Border" Property="MinWidth" Value="0"/>
                            </Trigger>
                            <Trigger Property="Role" Value="TopLevelItem">
                                <Setter Property="Margin" Value="0"/>
                                <Setter Property="FontWeight" Value="Normal"/>
                                <Setter TargetName="Border" Property="MinWidth" Value="0"/>
                            </Trigger>
                            <Trigger Property="Role" Value="SubmenuHeader">
                                <Setter TargetName="ArrowPanel" Property="Visibility" Value="Visible"/>
                            </Trigger>
                            <Trigger Property="IsHighlighted" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="{StaticResource UE_BluePrimary}"/>
                                <Setter Property="Foreground" Value="White"/>
                                <Setter TargetName="InputGestureText" Property="Foreground" Value="White"/>
                                <Setter TargetName="ArrowPanel" Property="Fill" Value="White"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="{StaticResource UE_BlueSecondary}"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>

                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Foreground" Value="{StaticResource UE_TextDisabled}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 工具栏样式 -->
        <Style x:Key="ToolBarStyle" TargetType="ToolBar">
            <Setter Property="Background" Value="{StaticResource UE_LightBackground}"/>
            <Setter Property="BorderThickness" Value="0,0,0,1"/>
            <Setter Property="BorderBrush" Value="{StaticResource UE_Border}"/>
            <Setter Property="Height" Value="42"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ToolBar">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                Padding="{TemplateBinding Padding}">
                            <DockPanel>
                                <ToolBarPanel x:Name="PART_ToolBarPanel"
                                              IsItemsHost="true"
                                              Margin="0,1,2,2"/>
                            </DockPanel>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 工具栏按钮样式 -->
        <Style x:Key="ToolBarButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="{StaticResource UE_TextPrimary}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Normal"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="3">
                            <ContentPresenter x:Name="contentPresenter"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="{StaticResource UE_VeryLightBackground}"/>
                                <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource UE_BorderLight}"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="{StaticResource UE_BluePrimary}"/>
                                <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource UE_BlueSecondary}"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="contentPresenter" Property="TextElement.Foreground" Value="{StaticResource UE_TextDisabled}"/>
                                <Setter TargetName="border" Property="Background" Value="{StaticResource UE_DarkBackground}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 图标按钮样式（带动画效果） -->
        <Style x:Key="IconButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="{StaticResource UE_TextPrimary}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="Width" Value="32"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="3">
                            <ContentPresenter x:Name="contentPresenter"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"
                                            TextElement.Foreground="{TemplateBinding Foreground}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Trigger.EnterActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <ColorAnimation Storyboard.TargetName="border"
                                                          Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                          To="#FF2F2F2F"
                                                          Duration="0:0:0.15"/>
                                            <ColorAnimation Storyboard.TargetName="border"
                                                          Storyboard.TargetProperty="(Border.BorderBrush).(SolidColorBrush.Color)"
                                                          To="#FF707070"
                                                          Duration="0:0:0.15"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.EnterActions>
                                <Trigger.ExitActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <ColorAnimation Storyboard.TargetName="border"
                                                          Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                          To="Transparent"
                                                          Duration="0:0:0.15"/>
                                            <ColorAnimation Storyboard.TargetName="border"
                                                          Storyboard.TargetProperty="(Border.BorderBrush).(SolidColorBrush.Color)"
                                                          To="Transparent"
                                                          Duration="0:0:0.15"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.ExitActions>
                                <Setter Property="Foreground" Value="{StaticResource UE_BlueLight}"/>
                                <Setter TargetName="contentPresenter" Property="TextElement.Foreground" Value="{StaticResource UE_BlueLight}"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="{StaticResource UE_BluePrimary}"/>
                                <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource UE_BlueSecondary}"/>
                                <Setter Property="Foreground" Value="White"/>
                                <Setter TargetName="contentPresenter" Property="TextElement.Foreground" Value="White"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Foreground" Value="{StaticResource UE_TextDisabled}"/>
                                <Setter TargetName="contentPresenter" Property="TextElement.Foreground" Value="{StaticResource UE_TextDisabled}"/>
                                <Setter TargetName="border" Property="Background" Value="{StaticResource UE_DarkBackground}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 项目树样式 -->
        <Style x:Key="ProjectTreeStyle" TargetType="TreeView">
            <Setter Property="Background" Value="{StaticResource UE_DarkBackground}"/>
            <Setter Property="Foreground" Value="{StaticResource UE_TextPrimary}"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="FontSize" Value="12"/>
        </Style>

        <!-- 项目树节点样式 -->
        <Style x:Key="ProjectTreeItemStyle" TargetType="TreeViewItem">
            <Setter Property="Foreground" Value="{StaticResource UE_TextPrimary}"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Padding" Value="4,2"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TreeViewItem">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition/>
                            </Grid.RowDefinitions>
                            <Border x:Name="Bd"
                                    Grid.Row="0"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    Padding="{TemplateBinding Padding}"
                                    CornerRadius="2">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition/>
                                    </Grid.ColumnDefinitions>
                                    <ToggleButton x:Name="Expander"
                                                Grid.Column="0"
                                                ClickMode="Press"
                                                IsChecked="{Binding IsExpanded, RelativeSource={RelativeSource TemplatedParent}}"
                                                Width="16" Height="16"
                                                Background="Transparent"
                                                BorderThickness="0"
                                                Margin="0,0,2,0">
                                        <ToggleButton.Template>
                                            <ControlTemplate TargetType="ToggleButton">
                                                <Border Background="Transparent">
                                                    <Grid>
                                                        <Path x:Name="CollapsedPath"
                                                              Data="M 0,0 L 0,6 L 4,3 Z"
                                                              Fill="#FF9E9E9E"
                                                              HorizontalAlignment="Center"
                                                              VerticalAlignment="Center"/>
                                                        <Path x:Name="ExpandedPath"
                                                              Data="M 0,0 L 6,0 L 3,4 Z"
                                                              Fill="#FF9E9E9E"
                                                              HorizontalAlignment="Center"
                                                              VerticalAlignment="Center"
                                                              Visibility="Collapsed"/>
                                                    </Grid>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsChecked" Value="True">
                                                        <Setter TargetName="CollapsedPath" Property="Visibility" Value="Collapsed"/>
                                                        <Setter TargetName="ExpandedPath" Property="Visibility" Value="Visible"/>
                                                    </Trigger>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter TargetName="CollapsedPath" Property="Fill" Value="#FFCCCCCC"/>
                                                        <Setter TargetName="ExpandedPath" Property="Fill" Value="#FFCCCCCC"/>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </ToggleButton.Template>
                                    </ToggleButton>
                                    <ContentPresenter x:Name="PART_Header"
                                                    Grid.Column="1"
                                                    ContentSource="Header"
                                                    HorizontalAlignment="Left"
                                                    VerticalAlignment="Center"
                                                    Margin="4,0,0,0"/>
                                </Grid>
                            </Border>
                            <ItemsPresenter x:Name="ItemsHost" Grid.Row="1" Margin="20,0,0,0"/>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsExpanded" Value="False">
                                <Setter TargetName="ItemsHost" Property="Visibility" Value="Collapsed"/>
                            </Trigger>
                            <Trigger Property="HasItems" Value="False">
                                <Setter TargetName="Expander" Property="Visibility" Value="Hidden"/>
                            </Trigger>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter TargetName="Bd" Property="Background" Value="{StaticResource UE_BluePrimary}"/>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="Bd" Property="Background" Value="{StaticResource UE_LightBackground}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 画布样式 -->
        <Style x:Key="BlueprintCanvasStyle" TargetType="Canvas">
            <Setter Property="Background">
                <Setter.Value>
                    <DrawingBrush TileMode="Tile" Viewport="0,0,20,20" ViewportUnits="Absolute">
                        <DrawingBrush.Drawing>
                            <GeometryDrawing>
                                <GeometryDrawing.Geometry>
                                    <RectangleGeometry Rect="0,0,20,20"/>
                                </GeometryDrawing.Geometry>
                                <GeometryDrawing.Brush>
                                    <SolidColorBrush Color="#FF1E1E1E"/>
                                </GeometryDrawing.Brush>
                                <GeometryDrawing.Pen>
                                    <Pen Brush="#FF2A2A2A" Thickness="0.5"/>
                                </GeometryDrawing.Pen>
                            </GeometryDrawing>
                        </DrawingBrush.Drawing>
                    </DrawingBrush>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 节点样式 -->
        <Style x:Key="BlueprintNodeStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource UE_DarkBackground}"/>
            <Setter Property="BorderBrush" Value="{StaticResource UE_BluePrimary}"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="MinWidth" Value="140"/>
            <Setter Property="MinHeight" Value="70"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="BorderBrush" Value="{StaticResource UE_BlueLight}"/>
                    <Setter Property="Background" Value="{StaticResource UE_LightBackground}"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 工具箱样式 -->
        <Style x:Key="ToolboxStyle" TargetType="ListBox">
            <Setter Property="Background" Value="{StaticResource UE_DarkBackground}"/>
            <Setter Property="Foreground" Value="{StaticResource UE_TextPrimary}"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Disabled"/>
        </Style>

        <!-- 工具箱项样式 -->
        <Style x:Key="ToolboxItemStyle" TargetType="ListBoxItem">
            <Setter Property="Foreground" Value="{StaticResource UE_TextPrimary}"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ListBoxItem">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="1"
                                CornerRadius="3"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="{StaticResource UE_LightBackground}"/>
                                <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource UE_BorderLight}"/>
                            </Trigger>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter TargetName="border" Property="Background" Value="{StaticResource UE_BluePrimary}"/>
                                <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource UE_BlueSecondary}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 日志面板样式 -->
        <Style x:Key="LogPanelStyle" TargetType="ListBox">
            <Setter Property="Background" Value="{StaticResource UE_DarkBackground}"/>
            <Setter Property="Foreground" Value="{StaticResource UE_TextSecondary}"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="Padding" Value="8"/>
        </Style>

        <!-- 分割器样式 -->
        <Style x:Key="GridSplitterStyle" TargetType="GridSplitter">
            <Setter Property="Background" Value="{StaticResource UE_Border}"/>
            <Setter Property="BorderThickness" Value="0"/>
        </Style>

        <!-- Menu样式 -->
        <Style x:Key="UE_MenuStyle" TargetType="Menu">
            <Setter Property="Background" Value="{StaticResource UE_LightBackground}"/>
            <Setter Property="Foreground" Value="{StaticResource UE_TextPrimary}"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
            <Setter Property="Padding" Value="0"/>
            <Setter Property="Margin" Value="0"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="VerticalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="IsMainMenu" Value="True"/>
        </Style>

        <!-- 窗口控制按钮基础样式 -->
        <Style x:Key="WindowControlButtonStyle" TargetType="Button">
            <Setter Property="Width" Value="30"/>
            <Setter Property="Height" Value="30"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Foreground" Value="{StaticResource UE_TextPrimary}"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="ButtonBorder"
                                Background="{TemplateBinding Background}"
                                CornerRadius="0">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Trigger.EnterActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <ColorAnimation Storyboard.TargetName="ButtonBorder"
                                                          Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                          To="#FF404040"
                                                          Duration="0:0:0.2"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.EnterActions>
                                <Trigger.ExitActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <ColorAnimation Storyboard.TargetName="ButtonBorder"
                                                          Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                          To="Transparent"
                                                          Duration="0:0:0.2"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.ExitActions>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Background" Value="#FF505050"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 关闭按钮特殊样式 -->
        <Style x:Key="CloseButtonStyle" TargetType="Button" BasedOn="{StaticResource WindowControlButtonStyle}">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="ButtonBorder"
                                Background="{TemplateBinding Background}"
                                CornerRadius="0">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Trigger.EnterActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <ColorAnimation Storyboard.TargetName="ButtonBorder"
                                                          Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                          To="#FFE81123"
                                                          Duration="0:0:0.2"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.EnterActions>
                                <Trigger.ExitActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <ColorAnimation Storyboard.TargetName="ButtonBorder"
                                                          Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                          To="Transparent"
                                                          Duration="0:0:0.2"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.ExitActions>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Background" Value="#FFC50E1F"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- ContextMenu样式 -->
        <Style x:Key="UE_ContextMenuStyle" TargetType="ContextMenu">
            <Setter Property="Background" Value="{StaticResource UE_MediumBackground}"/>
            <Setter Property="BorderBrush" Value="{StaticResource UE_Border}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="2"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="HasDropShadow" Value="True"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ContextMenu">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="2"
                                Padding="{TemplateBinding Padding}">
                            <ScrollViewer Style="{DynamicResource {ComponentResourceKey ResourceId=MenuScrollViewer, TypeInTargetAssembly={x:Type FrameworkElement}}}">
                                <ItemsPresenter KeyboardNavigation.DirectionalNavigation="Cycle"/>
                            </ScrollViewer>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- ComboBox样式 -->
        <Style x:Key="UE_ComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Background" Value="{StaticResource UE_MediumBackground}"/>
            <Setter Property="Foreground" Value="{StaticResource UE_TextPrimary}"/>
            <Setter Property="BorderBrush" Value="{StaticResource UE_Border}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ComboBox">
                        <Grid>
                            <Border x:Name="border"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="2">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <ContentPresenter x:Name="contentPresenter"
                                                    Grid.Column="0"
                                                    ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                                    Content="{TemplateBinding SelectionBoxItem}"
                                                    ContentStringFormat="{TemplateBinding SelectionBoxItemStringFormat}"
                                                    HorizontalAlignment="Left"
                                                    VerticalAlignment="Center"
                                                    Margin="{TemplateBinding Padding}"/>
                                    <Path x:Name="arrow"
                                          Grid.Column="1"
                                          Data="M 0,0 L 4,4 L 8,0 Z"
                                          Fill="{StaticResource UE_TextSecondary}"
                                          HorizontalAlignment="Center"
                                          VerticalAlignment="Center"
                                          Margin="8,0"/>
                                </Grid>
                            </Border>
                            <Popup x:Name="PART_Popup"
                                   AllowsTransparency="True"
                                   IsOpen="{Binding IsDropDownOpen, RelativeSource={RelativeSource TemplatedParent}}"
                                   Placement="Bottom"
                                   PopupAnimation="Slide">
                                <Border Background="{StaticResource UE_LightBackground}"
                                        BorderBrush="{StaticResource UE_Border}"
                                        BorderThickness="1"
                                        CornerRadius="2"
                                        MinWidth="{TemplateBinding ActualWidth}">
                                    <ScrollViewer>
                                        <ItemsPresenter/>
                                    </ScrollViewer>
                                </Border>
                            </Popup>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource UE_BorderLight}"/>
                                <Setter TargetName="arrow" Property="Fill" Value="{StaticResource UE_TextPrimary}"/>
                            </Trigger>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource UE_BluePrimary}"/>
                                <Setter TargetName="border" Property="BorderThickness" Value="2"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- ComboBoxItem样式 -->
        <Style x:Key="UE_ComboBoxItemStyle" TargetType="ComboBoxItem">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="{StaticResource UE_TextPrimary}"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ComboBoxItem">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="{StaticResource UE_VeryLightBackground}"/>
                            </Trigger>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter TargetName="border" Property="Background" Value="{StaticResource UE_BluePrimary}"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 文本块样式 -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="{StaticResource UE_TextPrimary}"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Margin" Value="8,4"/>
        </Style>

        <Style x:Key="SubHeaderTextStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="{StaticResource UE_TextSecondary}"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Normal"/>
            <Setter Property="Margin" Value="8,2"/>
        </Style>
    </Window.Resources>

    <Grid Margin="0">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>      <!-- 菜单栏 -->
            <RowDefinition Height="Auto"/>      <!-- 工具栏 -->
            <RowDefinition Height="*"/>         <!-- 主内容区 -->
            <RowDefinition Height="Auto"/>      <!-- 状态栏 -->
        </Grid.RowDefinitions>

        <!-- 菜单栏和窗口控制区域 -->
        <Border Grid.Row="0" Background="{StaticResource UE_LightBackground}"
                BorderBrush="{StaticResource UE_Border}" BorderThickness="0,0,0,1" Height="32">
            <Grid Margin="0" MouseLeftButtonDown="TitleBar_MouseLeftButtonDown"
                  WindowChrome.IsHitTestVisibleInChrome="True">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 菜单栏 -->
            <Menu Grid.Column="0" Style="{StaticResource UE_MenuStyle}" Background="Transparent"
                  WindowChrome.IsHitTestVisibleInChrome="True" Margin="8,0,0,0" VerticalAlignment="Center"
                  PreviewMouseDown="Menu_PreviewMouseDown" PreviewMouseUp="Menu_PreviewMouseUp" MouseDown="Menu_MouseDown" MouseUp="Menu_MouseUp">
            <MenuItem Header="项目" Style="{StaticResource MenuItemStyle}" PreviewMouseDown="MenuItem_PreviewMouseDown" Click="MenuItem_Click" MouseDown="MenuItem_MouseDown" MouseUp="MenuItem_MouseUp" PreviewMouseUp="MenuItem_PreviewMouseUp">
                <MenuItem Header="新建项目(_N)" Command="{Binding NewProjectCommand}" InputGestureText="Ctrl+N" Style="{StaticResource MenuItemStyle}"/>
                <MenuItem Header="打开项目(_O)" Command="{Binding OpenProjectCommand}" InputGestureText="Ctrl+O" Style="{StaticResource MenuItemStyle}"/>
                <Separator/>
                <MenuItem Header="保存项目(_S)" Command="{Binding SaveProjectCommand}" InputGestureText="Ctrl+S" Style="{StaticResource MenuItemStyle}"/>
                <MenuItem Header="另存为(_A)" Command="{Binding SaveProjectAsCommand}" InputGestureText="Ctrl+Shift+S" Style="{StaticResource MenuItemStyle}"/>
                <Separator/>
                <MenuItem Header="关闭项目(_C)" Command="{Binding CloseProjectCommand}" Style="{StaticResource MenuItemStyle}"/>
                <MenuItem Header="退出(_X)" Command="{Binding ExitCommand}" InputGestureText="Alt+F4" Style="{StaticResource MenuItemStyle}"/>
            </MenuItem>

            <MenuItem Header="编辑" Style="{StaticResource MenuItemStyle}" PreviewMouseDown="MenuItem_PreviewMouseDown">
                <MenuItem Header="撤销(_U)" Command="{Binding UndoCommand}" InputGestureText="Ctrl+Z" Style="{StaticResource MenuItemStyle}"/>
                <MenuItem Header="重做(_R)" Command="{Binding RedoCommand}" InputGestureText="Ctrl+Y" Style="{StaticResource MenuItemStyle}"/>
                <Separator/>
                <MenuItem Header="剪切(_T)" Command="{Binding CutCommand}" InputGestureText="Ctrl+X" Style="{StaticResource MenuItemStyle}"/>
                <MenuItem Header="复制(_C)" Command="{Binding CopyCommand}" InputGestureText="Ctrl+C" Style="{StaticResource MenuItemStyle}"/>
                <MenuItem Header="粘贴(_P)" Command="{Binding PasteCommand}" InputGestureText="Ctrl+V" Style="{StaticResource MenuItemStyle}"/>
            </MenuItem>

            <MenuItem Header="视图" Style="{StaticResource MenuItemStyle}" PreviewMouseDown="MenuItem_PreviewMouseDown">
                <MenuItem Header="项目树(_T)" IsCheckable="True" IsChecked="True" Style="{StaticResource MenuItemStyle}"/>
                <MenuItem Header="工具箱(_B)" IsCheckable="True" IsChecked="True" Style="{StaticResource MenuItemStyle}"/>
                <MenuItem Header="属性面板(_P)" IsCheckable="True" IsChecked="True" Style="{StaticResource MenuItemStyle}"/>
                <MenuItem Header="日志面板(_L)" IsCheckable="True" IsChecked="True" Style="{StaticResource MenuItemStyle}"/>
            </MenuItem>

            <MenuItem Header="调试" Style="{StaticResource MenuItemStyle}" PreviewMouseDown="MenuItem_PreviewMouseDown">
                <MenuItem Header="开始调试(_S)" Command="{Binding StartDebugCommand}" InputGestureText="F5" Style="{StaticResource MenuItemStyle}"/>
                <MenuItem Header="停止调试(_T)" Command="{Binding StopDebugCommand}" InputGestureText="Shift+F5" Style="{StaticResource MenuItemStyle}"/>
                <MenuItem Header="单步执行(_P)" Command="{Binding StepCommand}" InputGestureText="F10" Style="{StaticResource MenuItemStyle}"/>
                <MenuItem Header="切换断点(_B)" Command="{Binding BreakpointCommand}" InputGestureText="F9" Style="{StaticResource MenuItemStyle}"/>
            </MenuItem>

            <MenuItem Header="运行" Style="{StaticResource MenuItemStyle}" PreviewMouseDown="MenuItem_PreviewMouseDown">
                <MenuItem Header="启动(_S)" Command="{Binding StartCommand}" Style="{StaticResource MenuItemStyle}"/>
                <MenuItem Header="停止(_T)" Command="{Binding StopCommand}" Style="{StaticResource MenuItemStyle}"/>
                <MenuItem Header="暂停(_P)" Command="{Binding PauseCommand}" Style="{StaticResource MenuItemStyle}"/>
                <MenuItem Header="恢复(_R)" Command="{Binding ResumeCommand}" Style="{StaticResource MenuItemStyle}"/>
                <MenuItem Header="回原点(_H)" Command="{Binding HomeCommand}" Style="{StaticResource MenuItemStyle}"/>
                <MenuItem Header="急停(_E)" Command="{Binding EmergencyStopCommand}" Background="#FFFF4444" Style="{StaticResource MenuItemStyle}"/>
            </MenuItem>

            <MenuItem Header="工具" Style="{StaticResource MenuItemStyle}" PreviewMouseDown="MenuItem_PreviewMouseDown">
                <MenuItem Header="选项(_O)" Style="{StaticResource MenuItemStyle}"/>
                <MenuItem Header="系统配置(_C)" Style="{StaticResource MenuItemStyle}"/>
            </MenuItem>

            <MenuItem Header="帮助" Style="{StaticResource MenuItemStyle}" PreviewMouseDown="MenuItem_PreviewMouseDown">
                <MenuItem Header="查看帮助(_H)" InputGestureText="F1" Style="{StaticResource MenuItemStyle}"/>
                <MenuItem Header="关于(_A)" Style="{StaticResource MenuItemStyle}"/>
            </MenuItem>
        </Menu>

        <!-- 可拖动的标题栏区域 -->
        <Border Grid.Column="1" Background="Transparent"
                MouseLeftButtonDown="TitleBar_MouseLeftButtonDown"
                WindowChrome.IsHitTestVisibleInChrome="True"/>

        <!-- 窗口控制按钮 -->
        <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center"
                    WindowChrome.IsHitTestVisibleInChrome="True" Margin="0,0,4,0">
            <!-- 最小化按钮 -->
            <Button x:Name="MinimizeButton"
                    Style="{StaticResource WindowControlButtonStyle}"
                    Click="MinimizeButton_Click"
                    ToolTip="最小化">
                <TextBlock Text="─" FontSize="14" HorizontalAlignment="Center" VerticalAlignment="Center"/>
            </Button>

            <!-- 最大化/还原按钮 -->
            <Button x:Name="MaximizeButton"
                    Style="{StaticResource WindowControlButtonStyle}"
                    Click="MaximizeButton_Click"
                    ToolTip="最大化">
                <TextBlock Text="□" FontSize="14" HorizontalAlignment="Center" VerticalAlignment="Center"/>
            </Button>

            <!-- 关闭按钮 -->
            <Button x:Name="CloseButton"
                    Style="{StaticResource CloseButtonStyle}"
                    Click="CloseButton_Click"
                    ToolTip="关闭">
                <TextBlock Text="✕" FontSize="14" HorizontalAlignment="Center" VerticalAlignment="Center"/>
            </Button>
        </StackPanel>
            </Grid>
        </Border>

        <!-- 工具栏 -->
        <ToolBar Grid.Row="1" Style="{StaticResource ToolBarStyle}">
            <!-- 基本操作 -->
            <Button Content="新建" Command="{Binding NewProjectCommand}" Style="{StaticResource ToolBarButtonStyle}" ToolTip="新建项目 (Ctrl+N)"/>
            <Button Content="打开" Command="{Binding OpenProjectCommand}" Style="{StaticResource ToolBarButtonStyle}" ToolTip="打开项目 (Ctrl+O)"/>
            <Button Content="保存" Command="{Binding SaveCommand}" Style="{StaticResource ToolBarButtonStyle}" ToolTip="保存项目 (Ctrl+S)"/>
            
            <Separator/>
            
            <!-- 编辑操作 -->
            <Button Command="{Binding UndoCommand}" ToolTip="撤销 (Ctrl+Z)" Width="32" Height="32" Margin="2" Background="Transparent" BorderThickness="1" BorderBrush="Transparent">
                <Button.Template>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="ButtonBorder"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="3">
                            <TextBlock x:Name="IconText"
                                     Text="↶"
                                     FontSize="16"
                                     HorizontalAlignment="Center"
                                     VerticalAlignment="Center"
                                     Foreground="{StaticResource UE_TextPrimary}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Trigger.EnterActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <ColorAnimation Storyboard.TargetName="ButtonBorder"
                                                          Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                          To="#FF2F2F2F"
                                                          Duration="0:0:0.15"/>
                                            <ColorAnimation Storyboard.TargetName="ButtonBorder"
                                                          Storyboard.TargetProperty="(Border.BorderBrush).(SolidColorBrush.Color)"
                                                          To="#FF707070"
                                                          Duration="0:0:0.15"/>
                                            <ColorAnimation Storyboard.TargetName="IconText"
                                                          Storyboard.TargetProperty="(TextBlock.Foreground).(SolidColorBrush.Color)"
                                                          To="#FF40A9FF"
                                                          Duration="0:0:0.15"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.EnterActions>
                                <Trigger.ExitActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <ColorAnimation Storyboard.TargetName="ButtonBorder"
                                                          Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                          To="Transparent"
                                                          Duration="0:0:0.15"/>
                                            <ColorAnimation Storyboard.TargetName="ButtonBorder"
                                                          Storyboard.TargetProperty="(Border.BorderBrush).(SolidColorBrush.Color)"
                                                          To="Transparent"
                                                          Duration="0:0:0.15"/>
                                            <ColorAnimation Storyboard.TargetName="IconText"
                                                          Storyboard.TargetProperty="(TextBlock.Foreground).(SolidColorBrush.Color)"
                                                          To="#FFB0B0B0"
                                                          Duration="0:0:0.15"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.ExitActions>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Background" Value="#FF242424"/>
                                <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="#FF106EBE"/>
                                <Setter TargetName="IconText" Property="Foreground" Value="White"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="IconText" Property="Foreground" Value="#FF808080"/>
                                <Setter TargetName="ButtonBorder" Property="Background" Value="#FF101010"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Button.Template>
            </Button>
            <Button Command="{Binding RedoCommand}" ToolTip="重做 (Ctrl+Y)" Width="32" Height="32" Margin="2" Background="Transparent" BorderThickness="1" BorderBrush="Transparent">
                <Button.Template>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="ButtonBorder"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="3">
                            <TextBlock x:Name="IconText"
                                     Text="↷"
                                     FontSize="16"
                                     HorizontalAlignment="Center"
                                     VerticalAlignment="Center"
                                     Foreground="{StaticResource UE_TextPrimary}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Trigger.EnterActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <ColorAnimation Storyboard.TargetName="ButtonBorder"
                                                          Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                          To="#FF2F2F2F"
                                                          Duration="0:0:0.15"/>
                                            <ColorAnimation Storyboard.TargetName="ButtonBorder"
                                                          Storyboard.TargetProperty="(Border.BorderBrush).(SolidColorBrush.Color)"
                                                          To="#FF707070"
                                                          Duration="0:0:0.15"/>
                                            <ColorAnimation Storyboard.TargetName="IconText"
                                                          Storyboard.TargetProperty="(TextBlock.Foreground).(SolidColorBrush.Color)"
                                                          To="#FF40A9FF"
                                                          Duration="0:0:0.15"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.EnterActions>
                                <Trigger.ExitActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <ColorAnimation Storyboard.TargetName="ButtonBorder"
                                                          Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                          To="Transparent"
                                                          Duration="0:0:0.15"/>
                                            <ColorAnimation Storyboard.TargetName="ButtonBorder"
                                                          Storyboard.TargetProperty="(Border.BorderBrush).(SolidColorBrush.Color)"
                                                          To="Transparent"
                                                          Duration="0:0:0.15"/>
                                            <ColorAnimation Storyboard.TargetName="IconText"
                                                          Storyboard.TargetProperty="(TextBlock.Foreground).(SolidColorBrush.Color)"
                                                          To="#FFB0B0B0"
                                                          Duration="0:0:0.15"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.ExitActions>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Background" Value="#FF242424"/>
                                <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="#FF106EBE"/>
                                <Setter TargetName="IconText" Property="Foreground" Value="White"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="IconText" Property="Foreground" Value="#FF808080"/>
                                <Setter TargetName="ButtonBorder" Property="Background" Value="#FF101010"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Button.Template>
            </Button>
            
            <Separator/>
            
            <!-- 功能单元选择 -->
            <TextBlock Text="功能单元:" Foreground="{StaticResource UE_TextPrimary}" VerticalAlignment="Center" Margin="8,0"/>
            <Button Content="选择单元" Style="{StaticResource ToolBarButtonStyle}" ToolTip="选择功能单元"/>

            <Separator/>

            <!-- 调试区域 -->
            <Button Content="🐛 调试" Command="{Binding StartDebugCommand}" Style="{StaticResource ToolBarButtonStyle}" ToolTip="开始调试 (F5)"/>
            <Button Content="⏭ 单步" Command="{Binding StepCommand}" Style="{StaticResource ToolBarButtonStyle}" ToolTip="单步执行 (F10)"/>
            <Button Content="🔴 断点" Command="{Binding BreakpointCommand}" Style="{StaticResource ToolBarButtonStyle}" ToolTip="切换断点 (F9)"/>

            <Separator/>

            <!-- 运行区域 -->
            <Button Content="▶ 启动" Command="{Binding StartCommand}" Style="{StaticResource ToolBarButtonStyle}" Background="#FF8BC24A" Foreground="#FFEEEEEE" ToolTip="启动系统"/>
            <Button Content="⏹ 停止" Command="{Binding StopCommand}" Style="{StaticResource ToolBarButtonStyle}" Background="{StaticResource UE_Error}" ToolTip="停止系统"/>
            <Button Content="⏸ 暂停" Command="{Binding PauseCommand}" Style="{StaticResource ToolBarButtonStyle}" ToolTip="暂停系统"/>
            <Button Content="🏠 回原" Command="{Binding HomeCommand}" Style="{StaticResource ToolBarButtonStyle}" ToolTip="回原点"/>
            <Button Content="🚨 急停" Command="{Binding EmergencyStopCommand}" Style="{StaticResource ToolBarButtonStyle}" Background="{StaticResource UE_Error}" Foreground="#FFEEEEEE" ToolTip="紧急停止"/>

            <Separator/>

            <!-- 模式选择 -->
            <TextBlock Text="运行模式:" Foreground="{StaticResource UE_TextPrimary}" VerticalAlignment="Center" Margin="8,0"/>
            <ComboBox ItemsSource="{Binding RunModes}"
                      SelectedItem="{Binding SelectedRunMode}"
                      Width="120"
                      Background="{StaticResource UE_MediumBackground}"
                      Foreground="{StaticResource UE_TextPrimary}"
                      BorderBrush="{StaticResource UE_Border}"
                      ToolTip="选择运行模式"/>
        </ToolBar>

        <!-- 主内容区 -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>        <!-- 项目树 -->
                <ColumnDefinition Width="5"/>          <!-- 分割器 -->
                <ColumnDefinition Width="*"/>          <!-- 主编辑区 -->
                <ColumnDefinition Width="5"/>          <!-- 分割器 -->
                <ColumnDefinition Width="250"/>        <!-- 工具箱 -->
            </Grid.ColumnDefinitions>
            
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="5"/>
                <RowDefinition Height="200"/>          <!-- 日志区域 -->
            </Grid.RowDefinitions>

            <!-- 项目树 -->
            <Border Grid.Column="0" Grid.Row="0" Background="{StaticResource UE_DarkBackground}" BorderBrush="{StaticResource UE_Border}" BorderThickness="0,0,1,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <Border Grid.Row="0" Background="{StaticResource UE_LightBackground}" BorderBrush="{StaticResource UE_Border}" BorderThickness="0,0,0,1">
                        <TextBlock Text="📁 项目资源管理器" Style="{StaticResource HeaderTextStyle}" Padding="8"/>
                    </Border>

                    <TreeView Grid.Row="1"
                              ItemsSource="{Binding ProjectTree.RootNodes}"
                              SelectedItemChanged="TreeView_SelectedItemChanged"
                              Style="{StaticResource ProjectTreeStyle}">
                        <TreeView.ItemTemplate>
                            <HierarchicalDataTemplate ItemsSource="{Binding Children}">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="{Binding Icon}" Margin="0,0,6,0" Foreground="{StaticResource UE_Accent}"/>
                                    <TextBlock Text="{Binding Name}" Foreground="{StaticResource UE_TextPrimary}"/>
                                </StackPanel>
                            </HierarchicalDataTemplate>
                        </TreeView.ItemTemplate>
                        <TreeView.ItemContainerStyle>
                            <Style TargetType="TreeViewItem" BasedOn="{StaticResource ProjectTreeItemStyle}">
                                <Setter Property="IsExpanded" Value="{Binding IsExpanded, Mode=TwoWay}"/>
                                <Setter Property="IsSelected" Value="{Binding IsSelected, Mode=TwoWay}"/>
                                <EventSetter Event="MouseRightButtonDown" Handler="TreeViewItem_MouseRightButtonDown"/>
                                <EventSetter Event="MouseDoubleClick" Handler="TreeViewItem_MouseDoubleClick"/>
                            </Style>
                        </TreeView.ItemContainerStyle>
                    </TreeView>
                </Grid>
            </Border>

            <!-- 分割器 -->
            <GridSplitter Grid.Column="1" Grid.Row="0" Grid.RowSpan="3" Style="{StaticResource GridSplitterStyle}" HorizontalAlignment="Stretch"/>

            <!-- 主编辑区 -->
            <Border Grid.Column="2" Grid.Row="0" Background="{StaticResource UE_DarkBackground}" BorderBrush="{StaticResource UE_Border}" BorderThickness="1">
                <TabControl Background="Transparent" BorderThickness="0" SelectedIndex="{Binding SelectedEditorIndex}">
                    <TabControl.Resources>
                        <Style TargetType="TabItem">
                            <Setter Property="Foreground" Value="{StaticResource UE_TextPrimary}"/>
                            <Setter Property="Background" Value="{StaticResource UE_MediumBackground}"/>
                            <Setter Property="BorderThickness" Value="1,1,1,0"/>
                            <Setter Property="BorderBrush" Value="{StaticResource UE_Border}"/>
                            <Setter Property="Padding" Value="16,8"/>
                            <Setter Property="Margin" Value="0,0,2,0"/>
                            <Setter Property="FontSize" Value="13"/>
                            <Setter Property="FontWeight" Value="SemiBold"/>
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="TabItem">
                                        <Border x:Name="Border"
                                                Background="{TemplateBinding Background}"
                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                CornerRadius="4,4,0,0"
                                                Padding="{TemplateBinding Padding}">
                                            <ContentPresenter ContentSource="Header"
                                                            HorizontalAlignment="Center"
                                                            VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsSelected" Value="True">
                                                <Setter TargetName="Border" Property="Background" Value="{StaticResource UE_DarkBackground}"/>
                                                <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource UE_BlueSecondary}"/>
                                                <Setter Property="Foreground" Value="{StaticResource UE_BlueLight}"/>
                                            </Trigger>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter TargetName="Border" Property="Background" Value="{StaticResource UE_LightBackground}"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </TabControl.Resources>

                    <!-- 蓝图编辑器选项卡 -->
                    <TabItem Header="🎨 蓝图编辑器">
                        <views:UEBlueprintEditorView DataContext="{Binding BlueprintEditor}"/>
                    </TabItem>

                    <!-- SFC流程编辑器选项卡 -->
                    <TabItem Header="🔀 SFC编辑器">
                        <views:EnhancedSFCEditorView DataContext="{Binding SFCEditor}"/>
                    </TabItem>
                </TabControl>
            </Border>

            <!-- 分割器 -->
            <GridSplitter Grid.Column="3" Grid.Row="0" Grid.RowSpan="3" Style="{StaticResource GridSplitterStyle}" HorizontalAlignment="Stretch"/>

            <!-- 工具箱 -->
            <Border Grid.Column="4" Grid.Row="0" Background="{StaticResource UE_DarkBackground}" BorderBrush="{StaticResource UE_Border}" BorderThickness="1,0,0,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <Border Grid.Row="0" Background="{StaticResource UE_LightBackground}" BorderBrush="{StaticResource UE_Border}" BorderThickness="0,0,0,1">
                        <TextBlock Text="🧰 工具箱" Style="{StaticResource HeaderTextStyle}" Padding="8"/>
                    </Border>

                    <!-- 搜索框 -->
                    <Border Grid.Row="1" Background="{StaticResource UE_MediumBackground}" Padding="8">
                        <TextBox Text="{Binding Toolbox.SearchText, UpdateSourceTrigger=PropertyChanged}"
                                 Background="{StaticResource UE_DarkBackground}"
                                 Foreground="{StaticResource UE_TextPrimary}"
                                 BorderBrush="{StaticResource UE_Border}"
                                 BorderThickness="1"
                                 Padding="8,4"
                                 FontSize="12"/>
                    </Border>

                    <!-- 节点列表 -->
                    <TreeView Grid.Row="2"
                              ItemsSource="{Binding Toolbox.Categories}"
                              Background="Transparent"
                              Foreground="{StaticResource UE_TextPrimary}"
                              BorderThickness="0"
                              Margin="8">
                        <TreeView.ItemTemplate>
                            <HierarchicalDataTemplate ItemsSource="{Binding Nodes}">
                                <StackPanel Orientation="Horizontal" Visibility="{Binding IsVisible, Converter={StaticResource BoolToVisConverter}}">
                                    <TextBlock Text="{Binding Icon}" Margin="0,0,6,0" Foreground="{StaticResource UE_Accent}"/>
                                    <TextBlock Text="{Binding Name}" Foreground="{StaticResource UE_TextPrimary}" FontWeight="SemiBold"/>
                                </StackPanel>
                                <HierarchicalDataTemplate.ItemTemplate>
                                    <DataTemplate>
                                        <Border Background="Transparent"
                                                Padding="4,2"
                                                Margin="1"
                                                CornerRadius="2"
                                                Visibility="{Binding IsVisible, Converter={StaticResource BoolToVisConverter}}"
                                                MouseLeftButtonDown="ToolboxNode_MouseLeftButtonDown"
                                                MouseMove="ToolboxNode_MouseMove"
                                                MouseLeftButtonUp="ToolboxNode_MouseLeftButtonUp"
                                                AllowDrop="False">
                                            <StackPanel Orientation="Horizontal">
                                                <TextBlock Text="{Binding Icon}" Margin="0,0,6,0" Foreground="{StaticResource UE_TextSecondary}"/>
                                                <TextBlock Text="{Binding Name}" Foreground="{StaticResource UE_TextSecondary}" ToolTip="{Binding Description}"/>
                                            </StackPanel>
                                            <Border.Style>
                                                <Style TargetType="Border">
                                                    <Style.Triggers>
                                                        <Trigger Property="IsMouseOver" Value="True">
                                                            <Setter Property="Background" Value="{StaticResource UE_LightBackground}"/>
                                                        </Trigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Border.Style>
                                        </Border>
                                    </DataTemplate>
                                </HierarchicalDataTemplate.ItemTemplate>
                            </HierarchicalDataTemplate>
                        </TreeView.ItemTemplate>
                        <TreeView.ItemContainerStyle>
                            <Style TargetType="TreeViewItem">
                                <Setter Property="IsExpanded" Value="{Binding IsExpanded, Mode=TwoWay}"/>
                                <Setter Property="Foreground" Value="{StaticResource UE_TextPrimary}"/>
                                <Setter Property="Padding" Value="4,2"/>
                                <Setter Property="Visibility" Value="{Binding IsVisible, Converter={StaticResource BoolToVisConverter}}"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="{StaticResource UE_LightBackground}"/>
                                    </Trigger>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter Property="Background" Value="{StaticResource UE_BluePrimary}"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </TreeView.ItemContainerStyle>
                    </TreeView>
                </Grid>
            </Border>

            <!-- 水平分割器 -->
            <GridSplitter Grid.Column="0" Grid.ColumnSpan="5" Grid.Row="1" Style="{StaticResource GridSplitterStyle}" VerticalAlignment="Stretch" Height="5"/>

            <!-- 日志区域 -->
            <Border Grid.Column="0" Grid.ColumnSpan="5" Grid.Row="2" Background="{StaticResource UE_DarkBackground}" BorderBrush="{StaticResource UE_Border}" BorderThickness="0,1,0,0">
                <TabControl Background="Transparent" BorderThickness="0">
                    <TabControl.Resources>
                        <Style TargetType="TabItem">
                            <Setter Property="Foreground" Value="{StaticResource UE_TextPrimary}"/>
                            <Setter Property="Background" Value="{StaticResource UE_MediumBackground}"/>
                            <Setter Property="BorderThickness" Value="1,1,1,0"/>
                            <Setter Property="BorderBrush" Value="{StaticResource UE_Border}"/>
                            <Setter Property="Padding" Value="12,6"/>
                            <Setter Property="Margin" Value="0,0,2,0"/>
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="TabItem">
                                        <Border x:Name="Border"
                                                Background="{TemplateBinding Background}"
                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                CornerRadius="4,4,0,0"
                                                Padding="{TemplateBinding Padding}">
                                            <ContentPresenter ContentSource="Header"
                                                            HorizontalAlignment="Center"
                                                            VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsSelected" Value="True">
                                                <Setter TargetName="Border" Property="Background" Value="{StaticResource UE_BluePrimary}"/>
                                            </Trigger>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter TargetName="Border" Property="Background" Value="{StaticResource UE_LightBackground}"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </TabControl.Resources>

                    <TabItem Header="📋 输出">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <!-- 日志过滤器 -->
                            <Border Grid.Row="0" Background="{StaticResource UE_MediumBackground}" BorderBrush="{StaticResource UE_Border}" BorderThickness="0,0,0,1">
                                <StackPanel Orientation="Horizontal" Margin="8" >
                                    <TextBlock Text="级别:" Foreground="{StaticResource UE_TextPrimary}" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <ComboBox ItemsSource="{Binding LogPanel.LogLevels}"
                                              SelectedItem="{Binding LogPanel.FilterLevel}"
                                              Width="120"
                                              Style="{StaticResource UE_ComboBoxStyle}">
                                        <ComboBox.ItemContainerStyle>
                                            <Style TargetType="ComboBoxItem" BasedOn="{StaticResource UE_ComboBoxItemStyle}"/>
                                        </ComboBox.ItemContainerStyle>
                                        <ComboBox.ItemTemplate>
                                            <DataTemplate>
                                                <TextBlock Text="{Binding}" Foreground="{StaticResource UE_TextPrimary}"/>
                                            </DataTemplate>
                                        </ComboBox.ItemTemplate>
                                    </ComboBox>

                                    <TextBlock Text="来源:" Foreground="{StaticResource UE_TextPrimary}" VerticalAlignment="Center" Margin="16,0,8,0"/>
                                    <TextBox Text="{Binding LogPanel.FilterSource, UpdateSourceTrigger=PropertyChanged}"
                                             Width="150"
                                             Background="{StaticResource UE_DarkBackground}"
                                             Foreground="{StaticResource UE_TextPrimary}"
                                             BorderBrush="{StaticResource UE_Border}"
                                             Padding="6,4"/>

                                    <Button Content="🗑 清除" Command="{Binding LogPanel.ClearLogsCommand}"
                                            Style="{StaticResource ToolBarButtonStyle}"
                                            Margin="16,0,0,0"/>
                                </StackPanel>
                            </Border>

                            <!-- 日志列表 -->
                            <ListBox Grid.Row="1"
                                     ItemsSource="{Binding LogPanel.FilteredEntries}"
                                     SelectedItem="{Binding LogPanel.SelectedEntry}"
                                     Style="{StaticResource LogPanelStyle}"
                                     ScrollViewer.HorizontalScrollBarVisibility="Auto"
                                     ScrollViewer.VerticalScrollBarVisibility="Auto">
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding FormattedMessage}"
                                                   Foreground="{StaticResource UE_TextSecondary}"
                                                   FontFamily="Consolas"
                                                   FontSize="11"
                                                   Padding="4,2"/>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                        </Grid>
                    </TabItem>

                    <TabItem Header="❌ 错误列表">
                        <Grid Background="{StaticResource UE_DarkBackground}">
                            <TextBlock Text="暂无错误信息"
                                       Foreground="{StaticResource UE_TextSecondary}"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       FontStyle="Italic"/>
                        </Grid>
                    </TabItem>

                    <TabItem Header="🔍 查找结果">
                        <Grid Background="{StaticResource UE_DarkBackground}">
                            <TextBlock Text="暂无查找结果"
                                       Foreground="{StaticResource UE_TextSecondary}"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       FontStyle="Italic"/>
                        </Grid>
                    </TabItem>

                    <TabItem Header="📊 性能监控">
                        <Grid Background="{StaticResource UE_DarkBackground}">
                            <TextBlock Text="性能监控数据将在此显示"
                                       Foreground="{StaticResource UE_TextSecondary}"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       FontStyle="Italic"/>
                        </Grid>
                    </TabItem>
                </TabControl>
            </Border>
        </Grid>

        <!-- 状态栏 -->
        <StatusBar Grid.Row="3" Background="{StaticResource UE_BluePrimary}" Foreground="{StaticResource UE_TextPrimary}">
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="🟢" Margin="0,0,4,0"/>
                    <TextBlock Text="{Binding StatusMessage}" FontSize="12"/>
                </StackPanel>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="系统就绪" Margin="0,0,12,0" FontSize="12"/>
                    <ProgressBar Width="120" Height="16"
                                 IsIndeterminate="{Binding IsBusy}"
                                 Visibility="{Binding IsBusy, Converter={StaticResource BoolToVisConverter}}"
                                 Background="{StaticResource UE_MediumBackground}"
                                 Foreground="{StaticResource UE_Accent}"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
